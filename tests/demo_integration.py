#!/usr/bin/env python3
"""
Demo script showing complete AutoTrader integration
"""

import os
import subprocess
import asyncio
import sys

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.infrastructure.telegram.telegram_api_client import TelegramAPIClient

class AutoTraderDemo:
    """Demo class for AutoTrader integration"""
    
    def __init__(self):
        self.bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
        self.chat_id = os.getenv('TELEGRAM_CHAT_ID')
        self.bot_script = "./bot.sh"
    
    async def run_demo(self):
        """Run complete integration demo"""
        print("🎬 AutoTrader Integration Demo")
        print("==============================")
        
        # 1. Show system status
        await self.demo_system_status()
        
        # 2. Test CLI commands
        await self.demo_cli_commands()
        
        # 3. Test Telegram integration
        await self.demo_telegram_integration()
        
        # 4. Show full help
        await self.demo_help_system()
        
        print("\n🎉 Demo completed successfully!")
        print("\n📋 Summary:")
        print("✅ CLI Interface: Complete control via bot.sh")
        print("✅ Telegram Integration: Ready for remote control")  
        print("✅ Docker Support: Production deployment ready")
        print("✅ Unified System: Single entry point for all operations")
    
    async def demo_system_status(self):
        """Demo system status command"""
        print("\n📊 1. System Status Check")
        print("=" * 40)
        
        result = await self.run_command([self.bot_script, "system-status"])
        print(f"Exit code: {result[0]}")
        if result[0] == 0:
            print("✅ System status command working")
        
    async def demo_cli_commands(self):
        """Demo CLI commands"""
        print("\n🖥️  2. CLI Commands Demo")
        print("=" * 40)
        
        # Test version
        result = await self.run_command([self.bot_script, "version"])
        if result[0] == 0:
            print("✅ Version command working")
        
        # Test list
        result = await self.run_command([self.bot_script, "list"])
        if result[0] == 0:
            print("✅ List command working")
        
        print("📝 Available commands:")
        print("  • ./bot.sh setup - Quick setup")
        print("  • ./bot.sh start-all - Start everything")
        print("  • ./bot.sh telegram - Start Telegram bot")
        print("  • ./bot.sh start <symbol> - Start trading bot")
    
    async def demo_telegram_integration(self):
        """Demo Telegram integration"""
        print("\n📱 3. Telegram Integration Demo")
        print("=" * 40)
        
        if not self.bot_token or not self.chat_id:
            print("⚠️ Telegram not configured (set TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID)")
            return
        
        try:
            async with TelegramAPIClient(self.bot_token, self.chat_id) as client:
                # Send demo message
                await client.send_message(
                    self.chat_id,
                    "🎬 <b>AutoTrader Integration Demo</b>\n\n"
                    "✅ Telegram API: Working\n"
                    "✅ Bot Integration: Active\n"
                    "✅ Command System: Ready\n\n"
                    "🚀 Use <code>/help</code> in Telegram for full commands!"
                )
                print("✅ Telegram message sent successfully")
                print("📱 Bot can send notifications for:")
                print("  • Trading bot status updates")
                print("  • Command execution results")
                print("  • System alerts and notifications")
                
        except Exception as e:
            print(f"❌ Telegram integration error: {e}")
    
    async def demo_help_system(self):
        """Demo help system"""
        print("\n📚 4. Help System Demo")
        print("=" * 40)
        
        print("🔍 Complete command structure:")
        print("   ./bot.sh <command> [options]")
        print("")
        print("📋 Command categories:")
        print("  • SYSTEM: setup, start-all, stop-all, system-status")
        print("  • TELEGRAM: telegram, telegram-deploy")
        print("  • TRADING: start, stop, restart, list, status, logs")
        print("  • UTILITY: help, version")
        print("")
        print("🎯 Perfect for:")
        print("  • Server deployment with manual control")
        print("  • Remote management via Telegram")
        print("  • Development and testing")
    
    async def run_command(self, command):
        """Run shell command and return result"""
        try:
            process = await asyncio.create_subprocess_exec(
                *command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            return process.returncode or 0, stdout.decode('utf-8'), stderr.decode('utf-8')
            
        except Exception as e:
            return 1, "", str(e)

async def main():
    """Main demo function"""
    demo = AutoTraderDemo()
    await demo.run_demo()

if __name__ == "__main__":
    asyncio.run(main()) 