#!/bin/bash
# AutoTrader Bot - Compact Version (Server Deployment Script)
# Focused on core deployment and system operations
# Complex logic moved to Python CLI tools

set -euo pipefail

# Version and basic config
SCRIPT_VERSION="3.2.0"
VERSION="$SCRIPT_VERSION"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DOCKER_CMD="docker"

# ===============================
# Constants and Utilities (inlined - no external dependencies)
# ===============================

# Directory constants
AUTOTRADER_DIR="$HOME/.autotrader"
CREDENTIALS_DIR="$AUTOTRADER_DIR/credentials"
CONFIG_DIR="./configs"
DATA_DIR="./data"
LOGS_DIR="./logs"

# Docker image names
TELEGRAM_IMAGE="${TELEGRAM_IMAGE:-autotrader-telegram:latest}"
TRADER_IMAGE="${TRADER_IMAGE:-autotrader-trader:latest}"

# Environment variable names
TELEGRAM_BOT_TOKEN_VAR="TELEGRAM_BOT_TOKEN"
BYBIT_API_KEY_VAR="BYBIT_API_KEY"
BYBIT_API_SECRET_VAR="BYBIT_API_SECRET"
BYBIT_SECRET_KEY_VAR="BYBIT_SECRET_KEY"

# Default values
DEFAULT_TRADING_AMOUNT="50"
DEFAULT_SYMBOL_SUFFIX="/USDT:USDT"
DEFAULT_DIRECTION="LONG"
DEFAULT_TEST_MODE="false"
DEFAULT_CREDENTIAL_PROFILE="default"
CREDENTIAL_FILE_PERMISSIONS="600"

# Utility functions (inlined)
ensure_directories() {
    mkdir -p "$AUTOTRADER_DIR" "$CREDENTIALS_DIR" "$CONFIG_DIR" "$DATA_DIR" "$LOGS_DIR"
    chmod 755 "$AUTOTRADER_DIR" "$CREDENTIALS_DIR" 2>/dev/null || true
}

normalize_symbol() {
    local symbol="$1"
    local upper_symbol=$(echo "$symbol" | tr '[:lower:]' '[:upper:]')

    if [[ "$upper_symbol" =~ / ]]; then
        echo "$upper_symbol"
        return
    fi

    if [[ "$upper_symbol" =~ USDT$ ]]; then
        local base_symbol="${upper_symbol%USDT}"
        echo "${base_symbol}/USDT:USDT"
    else
        echo "${upper_symbol}$DEFAULT_SYMBOL_SUFFIX"
    fi
}

get_container_name() {
    local symbol="$1"
    local base_symbol=$(echo "$symbol" | cut -d'/' -f1 | tr '[:upper:]' '[:lower:]')
    base_symbol="${base_symbol%usdt}"
    echo "${base_symbol}usdt"
}

# Auto-load .env file if exists
if [[ -f ".env" ]]; then
    source .env
fi

# Environment variables with defaults
TELEGRAM_BOT_TOKEN="${TELEGRAM_BOT_TOKEN:-}"

# ===============================
# Additional Utility Functions
# ===============================
# (Core utilities are now in src/core/shell_constants.sh)

check_docker() {
    if ! command -v docker &> /dev/null; then
        echo "❌ Docker is not installed or not in PATH"
        echo "Please install Docker and try again"
        return 1
    fi
    
    if ! docker info &> /dev/null; then
        echo "❌ Docker daemon is not running"
        echo "Please start Docker and try again"
        return 1
    fi
    
    return 0
}

check_python() {
    if ! command -v python3 &> /dev/null; then
        echo "❌ Python3 is not installed"
        return 1
    fi
    return 0
}

# Docker CLI wrapper - runs Python CLI commands in Docker container
run_docker_cli() {
    local cli_script="$1"
    shift

    # Check if Docker is available
    if ! check_docker; then
        echo "❌ Docker is required for this command"
        return 1
    fi

    # Ensure directories exist for volume mounts
    ensure_directories

    # Get absolute paths for volume mounts
    local ABS_CONFIG_DIR="$(realpath "$CONFIG_DIR")"
    local ABS_DATA_DIR="$(realpath "$DATA_DIR")"
    local ABS_LOGS_DIR="$(realpath "$LOGS_DIR")"
    local ABS_AUTOTRADER_DIR="$(realpath "$AUTOTRADER_DIR")"

    # Run Python CLI in Docker container (no -it for non-interactive use)
    docker run --rm \
        -v "$ABS_CONFIG_DIR:/app/configs" \
        -v "$ABS_DATA_DIR:/app/data" \
        -v "$ABS_LOGS_DIR:/app/logs" \
        -v "$ABS_AUTOTRADER_DIR:/root/.autotrader" \
        -v /var/run/docker.sock:/var/run/docker.sock \
        -e BYBIT_API_KEY="${BYBIT_API_KEY:-}" \
        -e BYBIT_API_SECRET="${BYBIT_API_SECRET:-}" \
        -e BYBIT_SECRET_KEY="${BYBIT_SECRET_KEY:-}" \
        "$TELEGRAM_IMAGE" \
        python3 "src/cli/${cli_script}.py" "$@"
}

# ===============================
# System Setup Functions
# ===============================

extract_template_from_image() {
    echo "📄 Extracting template config from Docker image..."

    # Pull trader image if not exists
    if ! docker image inspect "$TRADER_IMAGE" &>/dev/null; then
        echo "🐳 Pulling trader image: $TRADER_IMAGE"
        if ! docker pull "$TRADER_IMAGE"; then
            echo "❌ Failed to pull trader image"
            return 1
        fi
    fi

    # Extract template.json from image
    local temp_container="temp_extract_$$"

    # Create temporary container and copy template
    if docker create --name "$temp_container" "$TRADER_IMAGE" >/dev/null 2>&1; then
        if docker cp "$temp_container:/app/template.json" "$CONFIG_DIR/template.json" 2>/dev/null; then
            echo "✅ Extracted template.json from image"
        else
            echo "⚠️ Could not extract template.json from image, creating basic template..."
            create_basic_template
        fi
        docker rm "$temp_container" >/dev/null 2>&1
    else
        echo "⚠️ Could not create temporary container, creating basic template..."
        create_basic_template
    fi
}

create_basic_template() {
    cat > "$CONFIG_DIR/template.json" << 'EOF'
{
  "symbol": "SYMBOL_PLACEHOLDER",
  "exchange": "bybit",
  "direction": "LONG",
  "amount": 50.0,
  "use_test_mode": false,
  "use_sandbox": false,
  "order_type": "limit",
  "signal_cooldown_minutes": 3.0,
  "trading_loop_interval_seconds": 10,
  "log_level": "INFO",
  "save_trades_to_csv": true,
  "enable_notifications": true
}
EOF
}

setup_environment() {
    echo "🔧 AutoTrader Complete Setup"
    echo "============================"

    # Check and install Docker (Linux only)
    if ! command -v docker &> /dev/null; then
        if [[ "$OSTYPE" == "linux-gnu"* ]]; then
            echo "🐳 Installing Docker..."
            curl -fsSL https://get.docker.com -o get-docker.sh
            sudo sh get-docker.sh
            sudo usermod -aG docker $USER
            rm get-docker.sh
            echo "✅ Docker installed. Please log out and back in."
        else
            echo "❌ Please install Docker manually for your OS"
            return 1
        fi
    fi

    # Verify Docker is running
    if ! docker info &> /dev/null; then
        echo "❌ Docker daemon is not running. Please start Docker."
        return 1
    else
        echo "✅ Docker is running"
    fi

    # Pull Docker images first (before creating configs)
    if check_docker; then
        echo "🐳 Pulling Docker images..."
        docker pull "$TELEGRAM_IMAGE" 2>/dev/null || echo "⚠️ Could not pull Telegram image"
        docker pull "$TRADER_IMAGE" 2>/dev/null || echo "⚠️ Could not pull Trader image"
    fi

    # Create directories and config files
    echo "📁 Creating directories and config files..."
    ensure_directories

    # Extract template config from Docker image
    if [[ ! -f "$CONFIG_DIR/template.json" ]]; then
        extract_template_from_image
        echo "✅ Created template config: $CONFIG_DIR/template.json"
    else
        echo "✅ Template config already exists"
    fi

    # Check Telegram setup
    echo "📱 Checking Telegram configuration..."
    if [[ -z "$TELEGRAM_BOT_TOKEN" ]]; then
        echo "⚠️ Telegram not configured. Set environment variable:"
        echo "   export TELEGRAM_BOT_TOKEN='your_bot_token'"
    else
        echo "✅ Telegram configured"
    fi

    echo ""
    echo "🎉 Setup finished!"
    echo ""
    echo "📋 Next steps:"
    echo "1. Configure Telegram (if not done):"
    echo "   export TELEGRAM_BOT_TOKEN='your_token'"
    echo "2. Setup Telegram bot authorization:"
    echo "   ./bot.sh setup-auth"
    echo "3. Start Telegram bot:"
    echo "   ./bot.sh telegram"
    echo "4. Use Telegram commands to manage trading bots"
}

upgrade_script() {
    echo "🔄 Upgrading bot.sh from GitHub Gist..."
    echo "========================================"

    # Check if curl is available
    if ! command -v curl &> /dev/null; then
        echo "❌ curl is required for upgrade. Please install curl first."
        return 1
    fi

    local gist_url="${BOT_GIST_URL:-https://gist.githubusercontent.com/hoangtrung99/73593690940ff91015063f2b6f9366a3/raw/autotrader.sh}"
    local script_path="$(realpath "${BASH_SOURCE[0]}")"
    local backup_file="${script_path}.backup.$(date +%Y%m%d-%H%M%S)"

    echo "📄 Current script: $script_path"
    echo "💾 Backup will be saved to: $backup_file"
    echo "🌐 Gist URL: $gist_url"

    # Create backup
    if cp "$script_path" "$backup_file"; then
        echo "✅ Backup created: $backup_file"
    else
        echo "❌ Failed to create backup. Aborting upgrade."
        return 1
    fi

    # Download new version to temporary file
    echo ""
    echo "📥 Downloading latest version..."
    local temp_file=$(mktemp)

    if curl -fsSL "$gist_url" -o "$temp_file"; then
        echo "✅ Downloaded successfully"

        # Show version comparison if possible
        local current_version=$(grep "^SCRIPT_VERSION=" "$script_path" | cut -d'"' -f2 2>/dev/null || echo "unknown")
        local new_version=$(grep "^SCRIPT_VERSION=" "$temp_file" | cut -d'"' -f2 2>/dev/null || echo "unknown")

        echo ""
        echo "📊 Version Comparison:"
        echo "Current: $current_version"
        echo "New:     $new_version"

        # Confirm upgrade
        echo ""
        echo "❓ Do you want to proceed with the upgrade? (y/N)"
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            echo "❌ Upgrade cancelled"
            rm -f "$temp_file"
            return 0
        fi

        # Replace current script with new version
        echo ""
        echo "🔄 Installing new version..."
        if mv "$temp_file" "$script_path"; then
            chmod +x "$script_path"
            echo "✅ Upgrade completed successfully!"
            echo ""
            echo "🎉 Upgrade completed!"
            echo "========================================"
            echo "📄 New version installed: $script_path"
            echo "💾 Backup available: $backup_file"
            echo ""
            echo "🔍 To verify the upgrade worked:"
            echo "   ./bot.sh version"
        else
            echo "❌ Failed to install new version"
            echo "🔄 Restoring from backup..."
            if cp "$backup_file" "$script_path"; then
                echo "✅ Restored from backup"
            else
                echo "❌ Failed to restore backup!"
            fi
            return 1
        fi
    else
        echo "❌ Failed to download new version"
        return 1
    fi
}

bind_global_command() {
    echo "🔗 Binding bot.sh as global 'traderbot' command..."
    echo "========================================"

    # Detect OS for different approaches
    local os="unknown"
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        os="linux"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        os="macos"
    fi

    local script_path="$(realpath "${BASH_SOURCE[0]}")"
    local installed=false

    # Try different installation paths
    local install_paths=(
        "$HOME/.local/bin/traderbot"
        "$HOME/bin/traderbot"
        "/usr/local/bin/traderbot"
    )

    for install_path in "${install_paths[@]}"; do
        local install_dir=$(dirname "$install_path")

        # Create directory if it doesn't exist
        if [[ ! -d "$install_dir" ]]; then
            if mkdir -p "$install_dir" 2>/dev/null; then
                echo "✅ Created directory: $install_dir"
            else
                echo "❌ Cannot create directory: $install_dir"
                continue
            fi
        fi

        # Try to install
        if cp "$script_path" "$install_path" 2>/dev/null; then
            chmod +x "$install_path"
            echo "✅ Installed to: $install_path"

            # Add to PATH if needed
            if [[ ":$PATH:" != *":$install_dir:"* ]]; then
                echo "💡 Adding $install_dir to PATH..."

                # Determine shell profile
                local shell_profile=""
                if [[ -n "$ZSH_VERSION" ]]; then
                    shell_profile="$HOME/.zshrc"
                elif [[ -n "$BASH_VERSION" ]]; then
                    shell_profile="$HOME/.bashrc"
                fi

                if [[ -n "$shell_profile" ]]; then
                    echo "export PATH=\"$install_dir:\$PATH\"" >> "$shell_profile"
                    echo "✅ Added to $shell_profile"
                    echo "💡 Run: source $shell_profile"
                fi
            fi

            installed=true
            break
        else
            echo "❌ Failed to install to: $install_path"
        fi
    done

    if [[ "$installed" == "true" ]]; then
        echo ""
        echo "🎉 Global 'traderbot' command installed!"
        echo "========================================"
        echo ""
        echo "📋 Usage Examples:"
        echo "  traderbot setup                    # Setup environment"
        echo "  traderbot start eth --amount 100   # Start ETH bot"
        echo "  traderbot list                     # List containers"
        echo "  traderbot telegram                 # Start Telegram bot"
        echo ""
        echo "💡 You may need to restart your terminal or run:"
        echo "   source ~/.bashrc  # or ~/.zshrc"
    else
        echo ""
        echo "❌ Failed to install global command"
        echo "💡 Manual installation:"
        echo "   sudo cp $script_path /usr/local/bin/traderbot"
        echo "   sudo chmod +x /usr/local/bin/traderbot"
    fi
}

unbind_global_command() {
    echo "🗑️  Removing 'traderbot' global command..."
    echo "========================================"

    # List of possible installation paths
    local remove_paths=(
        "/usr/local/bin/traderbot"
        "$HOME/.local/bin/traderbot"
        "$HOME/bin/traderbot"
    )

    local removed=false

    for remove_path in "${remove_paths[@]}"; do
        if [[ -f "$remove_path" ]]; then
            if rm "$remove_path" 2>/dev/null; then
                echo "✅ Removed: $remove_path"
                removed=true
            else
                echo "❌ Failed to remove: $remove_path (try with sudo)"
            fi
        fi
    done

    if [[ "$removed" == "true" ]]; then
        echo ""
        echo "✅ Global 'traderbot' command removed"
        echo "💡 You can still use: ./bot.sh"
    else
        echo ""
        echo "ℹ️  No global 'traderbot' command found"
    fi
}

# ===============================
# Docker Operations
# ===============================

start_telegram_bot() {
    echo "📱 Starting Telegram Bot (Docker)"
    echo "================================="

    # Check Docker
    if ! check_docker; then
        echo "❌ Docker is required to run Telegram bot"
        return 1
    fi

    # Check authorization setup
    echo "🔐 Checking Telegram authorization setup..."
    if ! check_telegram_auth_setup; then
        echo "❌ Cannot start bot without proper authorization setup"
        return 1
    fi
    echo ""

    # Check environment
    if [[ -z "$TELEGRAM_BOT_TOKEN" ]]; then
        echo "❌ Missing Telegram credentials"
        echo ""
        echo "💡 Set environment variable:"
        echo "   export TELEGRAM_BOT_TOKEN='your_bot_token'"
        return 1
    fi

    # Check if container already exists
    if docker ps -a --format "{{.Names}}" | grep -q "^telegram-bot$"; then
        echo "🔄 Stopping existing Telegram bot container..."
        docker stop telegram-bot 2>/dev/null || true
        docker rm telegram-bot 2>/dev/null || true
    fi

    # Start Telegram bot container
    echo "🐳 Starting Telegram bot container..."

    # Ensure directories exist
    ensure_directories

    # Convert relative paths to absolute paths to avoid Docker volume mounting issues
    local abs_config_dir="$(cd "$CONFIG_DIR" && pwd)"
    local abs_data_dir="$(cd "$DATA_DIR" && pwd)"
    local abs_logs_dir="$(cd "$LOGS_DIR" && pwd)"
    local abs_autotrader_dir="$AUTOTRADER_DIR"

    echo "📁 Volume mounts:"
    echo "   Config: $abs_config_dir -> /app/configs"
    echo "   Data: $abs_data_dir -> /app/data"
    echo "   Logs: $abs_logs_dir -> /app/logs"
    echo "   AutoTrader: $abs_autotrader_dir -> /root/.autotrader"

    docker run -d \
        --name telegram-bot \
        --restart unless-stopped \
        -e TELEGRAM_BOT_TOKEN="$TELEGRAM_BOT_TOKEN" \
        -e TELEGRAM_CHAT_ID="$TELEGRAM_CHAT_ID" \
        -e RUNNING_IN_CONTAINER="true" \
        -e HOST_CONFIG_DIR="$abs_config_dir" \
        -e HOST_DATA_DIR="$abs_data_dir" \
        -e HOST_LOGS_DIR="$abs_logs_dir" \
        -v "$abs_config_dir:/app/configs" \
        -v "$abs_data_dir:/app/data" \
        -v "$abs_logs_dir:/app/logs" \
        -v "$abs_autotrader_dir:/root/.autotrader" \
        -v /var/run/docker.sock:/var/run/docker.sock \
        "$TELEGRAM_IMAGE"

    if [[ $? -eq 0 ]]; then
        echo "✅ Telegram bot started successfully!"
        echo "📊 Container: telegram-bot"
        echo "🔗 Token: ${TELEGRAM_BOT_TOKEN:0:10}..."
        echo ""
        echo "📋 Monitor with:"
        echo "   docker logs telegram-bot"
        echo "   ./bot.sh simple-logs telegram-bot"
    else
        echo "❌ Failed to start Telegram bot container"
        return 1
    fi
}



start_trading_bot() {
    local symbol="$1"
    shift

    echo "🤖 Starting Trading Bot: $symbol"
    echo "================================"

    # Validate symbol
    if [[ -z "$symbol" ]]; then
        echo "❌ Symbol is required"
        echo "Usage: ./bot.sh start <symbol> [options]"
        return 1
    fi

    # Normalize symbol and generate container name
    local full_symbol=$(normalize_symbol "$symbol")
    local container_name=$(get_container_name "$symbol")

    # Check if container already exists and cleanup
    if docker ps -a --format "{{.Names}}" | grep -q "^${container_name}$"; then
        echo "🔄 Stopping existing container '$container_name'..."
        docker stop "$container_name" 2>/dev/null || true
        echo "�️ Removing existing container '$container_name'..."
        docker rm "$container_name" 2>/dev/null || true
        echo "✅ Container '$container_name' cleaned up successfully"
    fi

    # Create symbol-specific config
    local config_file="$CONFIG_DIR/${container_name}.json"
    if [[ ! -f "$config_file" ]]; then
        echo "📝 Creating config for $symbol..."

        # Create config from template (escape special characters for sed)
        local escaped_symbol=$(echo "$full_symbol" | sed 's/[\/&]/\\&/g')
        sed "s/SYMBOL_PLACEHOLDER/$escaped_symbol/g" "$CONFIG_DIR/template.json" > "$config_file"
        echo "✅ Created config: $config_file"
    fi

    # Parse additional options
    local amount="50"
    local dca_amount="25"  # Default DCA amount (50% of amount)
    local test_mode="false"
    local direction="LONG"
    local profile=""
    local cmd_api_key=""
    local cmd_api_secret=""

    while [[ $# -gt 0 ]]; do
        case $1 in
            --amount)
                amount="$2"
                shift 2
                ;;
            --dca-amount)
                dca_amount="$2"
                shift 2
                ;;
            --test-mode|--test)
                test_mode="true"
                shift
                ;;
            --live-mode|--live)
                test_mode="false"
                shift
                ;;
            --direction)
                direction="$2"
                shift 2
                ;;
            --profile)
                profile="$2"
                shift 2
                ;;
            --api-key)
                cmd_api_key="$2"
                shift 2
                ;;
            --api-secret)
                cmd_api_secret="$2"
                shift 2
                ;;
            *)
                echo "⚠️ Unknown option: $1"
                shift
                ;;
        esac
    done

    # Update config file with amount and dca_amount
    echo "📝 Updating config with amount=$amount, dca_amount=$dca_amount..."
    if [[ -f "$config_file" ]]; then
        # Use Python to update JSON config safely
        python3 -c "
import json
import sys

try:
    with open('$config_file', 'r') as f:
        config = json.load(f)

    # Update amounts
    config['amount'] = float('$amount')
    config['dca_amount'] = float('$dca_amount')

    with open('$config_file', 'w') as f:
        json.dump(config, f, indent=2)

    print('✅ Config updated successfully')
except Exception as e:
    print(f'❌ Failed to update config: {e}')
    sys.exit(1)
"
        if [[ $? -ne 0 ]]; then
            echo "❌ Failed to update config file"
            return 1
        fi
    fi

    # Resolve credentials using priority system
    if ! resolve_credentials "$profile" "$cmd_api_key" "$cmd_api_secret"; then
        echo ""
        echo "💡 Available credential sources (in priority order):"
        echo "   1. Profile: --profile <name> (highest priority)"
        echo "   2. Command line: --api-key <key> --api-secret <secret>"
        echo "   3. Environment: BYBIT_API_KEY, BYBIT_API_SECRET/BYBIT_SECRET_KEY"
        echo ""
        echo "📋 Examples:"
        echo "   ./bot.sh start btc --profile main --amount 100"
        echo "   ./bot.sh start btc --api-key 'key' --api-secret 'secret'"
        echo "   export BYBIT_API_KEY='key' && ./bot.sh start btc"
        echo ""
        echo "💡 Manage profiles:"
        echo "   python3 src/cli/credentials_cli.py list"
        echo "   python3 src/cli/credentials_cli.py store <profile>"
        return 1
    fi

    # Verify credentials are now available
    local api_key="${!BYBIT_API_KEY_VAR:-}"
    local api_secret="${!BYBIT_API_SECRET_VAR:-}"

    if [[ -z "$api_key" ]] || [[ -z "$api_secret" ]]; then
        echo "❌ Credential resolution failed"
        return 1
    fi

    # Check Docker is available
    if ! check_docker; then
        echo "❌ Docker is required to run trading bots"
        return 1
    fi

    # Start Docker container
    echo "🐳 Starting Docker container..."

    # Handle different execution contexts
    if [[ "${RUNNING_IN_CONTAINER:-}" == "true" ]]; then
        echo "🐳 Running in container - using host mount paths from environment"
        echo "🔍 DEBUG: RUNNING_IN_CONTAINER='${RUNNING_IN_CONTAINER:-}'"
        echo "🔍 DEBUG: HOST_CONFIG_DIR='${HOST_CONFIG_DIR:-}'"
        echo "🔍 DEBUG: HOST_DATA_DIR='${HOST_DATA_DIR:-}'"
        echo "🔍 DEBUG: HOST_LOGS_DIR='${HOST_LOGS_DIR:-}'"

        # When running in Telegram bot container, use the host paths that were mounted
        # These MUST be the actual host paths, not container paths
        local abs_config_dir="${HOST_CONFIG_DIR}"
        local abs_data_dir="${HOST_DATA_DIR}"
        local abs_logs_dir="${HOST_LOGS_DIR}"

        echo "🔍 DEBUG: abs_config_dir='$abs_config_dir'"
        echo "🔍 DEBUG: abs_data_dir='$abs_data_dir'"
        echo "🔍 DEBUG: abs_logs_dir='$abs_logs_dir'"

        # Validate that host paths are available and not empty
        if [[ -z "$abs_config_dir" || -z "$abs_data_dir" || -z "$abs_logs_dir" ]]; then
            echo "❌ ERROR: Host paths not available in container environment"
            echo "   HOST_CONFIG_DIR: '$HOST_CONFIG_DIR'"
            echo "   HOST_DATA_DIR: '$HOST_DATA_DIR'"
            echo "   HOST_LOGS_DIR: '$HOST_LOGS_DIR'"
            echo "💡 This indicates a problem with Telegram bot container setup"
            return 1
        fi

        echo "✅ Using host paths from environment variables"
    else
        echo "💻 Running on host - using local absolute paths"
        # Convert relative paths to absolute paths to avoid Docker volume mounting issues
        local abs_config_dir="$(cd "$CONFIG_DIR" && pwd)"
        local abs_data_dir="$(cd "$DATA_DIR" && pwd)"
        local abs_logs_dir="$(cd "$LOGS_DIR" && pwd)"
    fi

    echo "📁 Volume mounts:"
    echo "   Config: $abs_config_dir -> /app/configs"
    echo "   Data: $abs_data_dir -> /app/data"
    echo "   Logs: $abs_logs_dir -> /app/logs"

    # Prepare Docker command arguments
    local docker_args=(
        "run" "-d"
        "--name" "$container_name"
        "--restart" "unless-stopped"
        "-e" "$BYBIT_API_KEY_VAR=$api_key"
        "-e" "$BYBIT_API_SECRET_VAR=$api_secret"
        "-e" "BOT_CONFIG_FILE=configs/${container_name}.json"
        "-e" "TRADE_SYMBOL=$full_symbol"
        "-e" "TRADE_AMOUNT=$amount"
        "-e" "TRADE_DIRECTION=$direction"
        "-e" "TEST_MODE=$test_mode"
        "-v" "$abs_config_dir:/app/configs"
        "-v" "$abs_data_dir:/app/data"
        "-v" "$abs_logs_dir:/app/logs"
        "$TRADER_IMAGE"
        "python" "main.py" "--start"
    )

    # Add test flag if in test mode
    if [[ "$test_mode" == "true" ]]; then
        docker_args+=("--test")
    fi

    # Run Docker container
    docker "${docker_args[@]}"

    if [[ $? -eq 0 ]]; then
        echo "✅ Trading bot started successfully!"
        echo "📊 Container: $container_name"
        echo "💰 Symbol: $full_symbol"
        echo "💵 Amount: $amount USDT"
        echo "🧪 Test Mode: $test_mode"
        echo ""
        echo "📋 Monitor with:"
        echo "   ./bot.sh logs $container_name"
        echo "   python3 src/cli/autotrader_cli.py status $container_name"
    else
        echo "❌ Failed to start trading bot"
        return 1
    fi
}

start_all() {
    echo "🚀 Starting Complete AutoTrader System"
    echo "======================================"
    
    # Start Telegram bot in background
    echo "📱 Starting Telegram bot..."
    start_telegram_bot &
    
    echo "✅ System startup initiated"
    echo "📱 Telegram bot running in background"
    echo "💡 Use Telegram commands to manage trading bots"
}

stop_all() {
    echo "🛑 Stopping All AutoTrader Services"
    
    # Stop all containers
    if check_docker; then
        echo "🐳 Stopping Docker containers..."
        $DOCKER_CMD stop $(docker ps -q --filter "name=autotrader") 2>/dev/null || true
        echo "✅ All containers stopped"
    fi
    
    # Kill background processes
    pkill -f "telegram" 2>/dev/null || true
    
    echo "✅ All services stopped"
}

system_status() {
    echo "📊 AutoTrader System Status"
    echo "==========================="

    # Docker status
    if check_docker; then
        echo "✅ Docker: Running"
        local containers=$($DOCKER_CMD ps --format "table {{.Names}}\t{{.Status}}" | grep autotrader || echo "No containers")
        echo "🐳 Containers: $containers"
    else
        echo "❌ Docker: Not available"
    fi

    # Python status
    if check_python; then
        echo "✅ Python: $(python3 --version)"
    else
        echo "❌ Python: Not available"
    fi

    # Telegram status
    if [[ -n "$TELEGRAM_BOT_TOKEN" ]]; then
        echo "✅ Telegram: Configured"
    else
        echo "⚠️ Telegram: Not configured"
    fi
}

simple_logs() {
    local container_name="$1"
    local lines="${2:-50}"

    if [[ -z "$container_name" ]]; then
        echo "❌ Container name is required"
        return 1
    fi

    echo "📋 Logs for container: $container_name (last $lines lines)"
    echo "=" * 60

    if docker logs "$container_name" --tail "$lines" 2>/dev/null; then
        echo ""
        echo "✅ Logs retrieved successfully"
    else
        echo "❌ Failed to get logs for container: $container_name"
        echo "💡 Available containers:"
        docker ps --format "{{.Names}}" | head -5
        return 1
    fi
}

# ===============================
# Credential Management Functions
# ===============================

check_telegram_profile() {
    # Check if Telegram bot has set a profile for this session
    local telegram_profile_file="/tmp/traderbot_telegram_profile_$$"
    local telegram_profile_global="/tmp/traderbot_telegram_profile"

    if [[ -f "$telegram_profile_file" ]]; then
        cat "$telegram_profile_file"
        return 0
    elif [[ -f "$telegram_profile_global" ]]; then
        cat "$telegram_profile_global"
        return 0
    fi

    return 1
}

load_profile_credentials() {
    local profile="$1"

    if [[ -z "$profile" ]]; then
        return 1
    fi

    echo "🔄 Loading credentials from profile: $profile"

    # Use centralized credentials directory
    local creds_dir="$CREDENTIALS_DIR"

    # Try Python CLI first (JSON format)
    local json_file="$creds_dir/${profile}.json"
    if [[ -f "$json_file" ]]; then
        # Load from JSON format (Python CLI) - get export commands
        if command -v python3 &> /dev/null; then
            local result=$(python3 src/cli/credentials_cli.py load "$profile" 2>/dev/null)
            if [[ $? -eq 0 ]]; then
                # Parse the export commands
                local api_key=$(echo "$result" | grep "export $BYBIT_API_KEY_VAR=" | cut -d"'" -f2)
                local api_secret=$(echo "$result" | grep "export $BYBIT_API_SECRET_VAR=" | cut -d"'" -f2)

                if [[ -n "$api_key" ]] && [[ -n "$api_secret" ]]; then
                    export "$BYBIT_API_KEY_VAR"="$api_key"
                    export "$BYBIT_API_SECRET_VAR"="$api_secret"
                    echo "✅ Loaded credentials from profile: $profile"
                    return 0
                fi
            fi
        fi
    fi

    # Try legacy .env format (for backward compatibility)
    local env_file="$creds_dir/${profile}.env"
    if [[ -f "$env_file" ]]; then
        echo "🔄 Loading from .env format..."
        source "$env_file"
        if [[ -n "${!BYBIT_API_KEY_VAR}" ]] && [[ -n "${!BYBIT_API_SECRET_VAR}" ]]; then
            echo "✅ Loaded credentials from profile: $profile"
            return 0
        fi
    fi

    echo "❌ Failed to load credentials from profile: $profile"
    return 1
}

resolve_credentials() {
    local profile="$1"
    local cmd_api_key="$2"
    local cmd_api_secret="$3"

    echo "🔍 Resolving credentials with priority system..."

    # Priority 1: Profile credentials (from Telegram bot selection or command line)
    # Check Telegram profile first if no explicit profile provided
    if [[ -z "$profile" ]]; then
        local telegram_profile=$(check_telegram_profile 2>/dev/null)
        if [[ -n "$telegram_profile" ]]; then
            echo "🥇 Priority 1a: Found Telegram selected profile '$telegram_profile'"
            profile="$telegram_profile"
        fi
    fi

    if [[ -n "$profile" ]]; then
        echo "🥇 Priority 1b: Loading from profile '$profile'"
        if load_profile_credentials "$profile"; then
            return 0
        else
            echo "⚠️ Failed to load profile '$profile', trying next priority..."
        fi
    fi

    # Priority 2: Command line arguments
    if [[ -n "$cmd_api_key" ]] && [[ -n "$cmd_api_secret" ]]; then
        echo "🥈 Priority 2: Using command line credentials"
        export "$BYBIT_API_KEY_VAR"="$cmd_api_key"
        export "$BYBIT_API_SECRET_VAR"="$cmd_api_secret"
        echo "✅ Loaded credentials from command line"
        return 0
    fi

    # Priority 3: Environment variables
    local env_api_key="${!BYBIT_API_KEY_VAR:-}"
    local env_api_secret="${!BYBIT_API_SECRET_VAR:-${!BYBIT_SECRET_KEY_VAR:-}}"

    if [[ -n "$env_api_key" ]] && [[ -n "$env_api_secret" ]]; then
        echo "🥉 Priority 3: Using environment variables"
        export "$BYBIT_API_KEY_VAR"="$env_api_key"
        export "$BYBIT_API_SECRET_VAR"="$env_api_secret"
        echo "✅ Loaded credentials from environment"
        return 0
    fi

    # No credentials found
    echo "❌ No credentials found in any source"
    return 1
}

show_help() {
    cat << EOF
🤖 AutoTrader Bot - Compact Server Deployment Script

USAGE: $0 <command> [options]

📋 DEPLOYMENT COMMANDS:
    setup                    Complete environment setup (creates configs, directories)
    upgrade                  Upgrade script from GitHub Gist
    bind                     Install as global 'traderbot' command
    unbind                   Remove global 'traderbot' command
    start-all               Start complete system
    stop-all                Stop all services
    system-status           Show system status

📱 TELEGRAM BOT:
    telegram                Start Telegram bot (curl-based, minimal dependencies)
    setup-auth              Setup Telegram bot authorization (interactive wizard)
    config-auth             View/manage Telegram bot authorization config

🚀 TRADING BOTS:
    start <symbol> [opts]   Create trading bot

    Options:
      --amount <amount>     Trading amount (default: 50)
      --test               Enable test mode
      --live               Enable live mode (default)
      --profile <name>     Use credential profile (highest priority)
      --api-key <key>      API key (medium priority)
      --api-secret <secret> API secret (medium priority)
      --direction <dir>    Trading direction (LONG/SHORT)

📊 BOT MANAGEMENT (Use Docker CLI or Telegram):

    Docker CLI (runs Python commands in containers):
    ./bot.sh list
    ./bot.sh status <symbol>
    ./bot.sh logs <symbol>
    ./bot.sh stop <symbol>
    ./bot.sh restart <symbol>
    ./bot.sh remove <symbol>

    Telegram Commands:
    /list, /status, /logs, /stop, /restart, /remove

🔑 CREDENTIALS (Use Docker CLI):
    ./bot.sh list-credentials
    ./bot.sh store-credentials <profile> <api_key> <api_secret> [display_name]
    ./bot.sh load-credentials <profile>

EXAMPLES:
    # First time setup
    $0 setup                    # Complete environment setup (pulls images, extracts template)
    $0 setup-auth               # Setup Telegram bot authorization
    $0 bind                     # Install as 'traderbot' command (optional)

    # Trading operations (credential priority examples)
    $0 start eth --profile main --amount 100        # Use 'main' profile (highest priority)
    $0 start btc --api-key 'key' --api-secret 'sec' # Use command line credentials
    $0 start hyper --amount 50 --test               # Use environment variables + test mode

    # Traditional examples
    $0 start eth --amount 100   # Start ETH trading bot (live mode)
    $0 start btc --test         # Start BTC bot in test mode

    # System management
    $0 start-all               # Start complete system
    $0 telegram                # Start Telegram bot
    $0 system-status           # Check system status

    # Global command usage (after bind)
    traderbot start eth --amount 100
    traderbot list
    traderbot telegram

EOF
}

# ===============================
# Telegram Auth Setup Functions
# ===============================

check_telegram_auth_setup() {
    # Check if Telegram authorization is properly configured
    local auth_config="$AUTOTRADER_DIR/telegram_auth.json"

    # Check if auth config file exists
    if [[ ! -f "$auth_config" ]]; then
        echo "❌ Telegram bot authorization chưa được setup!"
        echo ""
        echo "🔐 Bạn cần setup authorization trước khi khởi động bot:"
        echo "   ./bot.sh setup-auth"
        echo ""
        echo "💡 Hoặc chạy lệnh sau để setup ngay:"
        echo ""
        read -p "Bạn có muốn setup authorization ngay bây giờ? (y/N): " setup_now

        if [[ "$setup_now" =~ ^[Yy]$ ]]; then
            echo ""
            setup_telegram_auth
            return $?
        else
            echo ""
            echo "⚠️ Bot không thể khởi động mà không có authorization setup."
            echo "   Chạy './bot.sh setup-auth' trước khi khởi động bot."
            return 1
        fi
    fi

    # Validate auth config structure
    if ! python3 -c "
import json
import sys
try:
    with open('$auth_config', 'r') as f:
        config = json.load(f)

    # Check required structure
    if 'authorized_users' not in config:
        print('❌ Config thiếu authorized_users section')
        sys.exit(1)

    if 'settings' not in config:
        print('❌ Config thiếu settings section')
        sys.exit(1)

    settings = config['settings']
    if 'creator_username' not in settings:
        print('❌ Config thiếu creator_username setting')
        sys.exit(1)

    print('✅ Authorization config hợp lệ')

except json.JSONDecodeError:
    print('❌ Config file không phải JSON hợp lệ')
    sys.exit(1)
except Exception as e:
    print(f'❌ Lỗi đọc config: {e}')
    sys.exit(1)
" 2>/dev/null; then
        echo "❌ Authorization config file bị lỗi!"
        echo ""
        echo "💡 Chạy lệnh sau để reset config:"
        echo "   ./bot.sh setup-auth"
        echo ""
        read -p "Bạn có muốn reset authorization config? (y/N): " reset_config

        if [[ "$reset_config" =~ ^[Yy]$ ]]; then
            echo ""
            setup_telegram_auth
            return $?
        else
            return 1
        fi
    fi

    echo "✅ Telegram authorization đã được setup"
    return 0
}

setup_telegram_auth() {
    echo "🔐 Telegram Bot Authorization Setup"
    echo "=================================="
    echo ""

    # Ensure .autotrader directory exists
    ensure_directories

    local auth_config="$AUTOTRADER_DIR/telegram_auth.json"

    echo "This wizard will help you set up Telegram bot authorization."
    echo "You'll need to provide a super admin user (yourself)."
    echo ""

    # Get super admin input
    local super_admin_input=""
    local super_admin_username=""
    local super_admin_id=""

    while [[ -z "$super_admin_input" ]]; do
        echo "👑 Enter super admin (username or user ID):"
        echo "   Examples: hoangtrungdev, @hoangtrungdev, or 1631630468"
        read -p "Super admin: " super_admin_input

        if [[ -z "$super_admin_input" ]]; then
            echo "❌ Super admin cannot be empty. Please try again."
            echo ""
        fi
    done

    # Parse input - detect if it's username or user ID
    if [[ "$super_admin_input" =~ ^[0-9]+$ ]]; then
        # It's a user ID
        super_admin_id="$super_admin_input"
        echo "📝 Enter username for user ID $super_admin_id (optional):"
        read -p "Username: " super_admin_username
        if [[ -z "$super_admin_username" ]]; then
            super_admin_username="user_$super_admin_id"
        fi
    else
        # It's a username - clean it up
        super_admin_username="${super_admin_input#@}"  # Remove @ if present
        echo "📝 Enter user ID for @$super_admin_username (optional, leave empty for auto-detection):"
        read -p "User ID: " super_admin_id
    fi

    # Create auth config
    echo ""
    echo "🔧 Creating authorization config..."

    local config_content='{
  "authorized_users": {}'

    # Add super admin if we have user ID
    if [[ -n "$super_admin_id" ]]; then
        config_content='{
  "authorized_users": {
    "'$super_admin_id'": {
      "username": "'$super_admin_username'",
      "role": "SUPER_ADMIN",
      "added_by": "setup_wizard",
      "added_at": "'$(date -u +"%Y-%m-%dT%H:%M:%S")'.000Z"
    }
  }'
    fi

    config_content="$config_content,
  \"settings\": {
    \"default_rejection_message\": \"❌ Bạn không có quyền sử dụng bot này. Liên hệ @$super_admin_username để được cấp quyền.\",
    \"super_admin_only_commands\": [
      \"/adduser\",
      \"/removeuser\",
      \"/listusers\"
    ],
    \"creator_username\": \"$super_admin_username\",
    \"audit_log\": true,
    \"strict_mode\": true,
    \"auto_promote_creator\": true
  }
}"

    # Write config file
    echo "$config_content" > "$auth_config"
    chmod 600 "$auth_config"

    echo "✅ Authorization config created: $auth_config"
    echo ""
    echo "📋 Configuration Summary:"
    echo "   Super Admin: @$super_admin_username"
    if [[ -n "$super_admin_id" ]]; then
        echo "   User ID: $super_admin_id"
    else
        echo "   User ID: Auto-detect on first use"
    fi
    echo "   Config File: $auth_config"
    echo ""
    echo "🎯 Next Steps:"
    echo "   1. Start your Telegram bot: ./bot.sh telegram"
    echo "   2. Send /start to your bot to auto-promote yourself"
    echo "   3. Use /adduser to add other users"
    echo ""
}

config_telegram_auth() {
    echo "⚙️ Telegram Bot Authorization Config"
    echo "===================================="
    echo ""

    local auth_config="$AUTOTRADER_DIR/telegram_auth.json"

    if [[ ! -f "$auth_config" ]]; then
        echo "❌ Authorization config not found: $auth_config"
        echo "💡 Run './bot.sh setup-auth' first to create the config"
        return 1
    fi

    echo "📋 Current Configuration:"
    echo "========================"

    # Display current config in a readable format
    if command -v python3 &> /dev/null; then
        python3 -c "
import json
import sys

try:
    with open('$auth_config', 'r') as f:
        config = json.load(f)

    print('👑 Super Admin Settings:')
    settings = config.get('settings', {})
    creator = settings.get('creator_username', 'Not set')
    print(f'   Creator: @{creator}')
    print(f'   Auto-promote: {settings.get(\"auto_promote_creator\", False)}')
    print(f'   Strict mode: {settings.get(\"strict_mode\", False)}')
    print(f'   Audit log: {settings.get(\"audit_log\", False)}')
    print()

    print('👥 Authorized Users:')
    users = config.get('authorized_users', {})
    if not users:
        print('   No users configured')
    else:
        for user_id, user_info in users.items():
            username = user_info.get('username', 'Unknown')
            role = user_info.get('role', 'USER')
            added_by = user_info.get('added_by', 'Unknown')
            print(f'   @{username} ({user_id}) - {role} (added by {added_by})')
    print()

    print('🚫 Restricted Commands:')
    restricted = settings.get('super_admin_only_commands', [])
    for cmd in restricted:
        print(f'   {cmd}')

except Exception as e:
    print(f'❌ Error reading config: {e}')
    sys.exit(1)
"
    else
        echo "⚠️ Python3 not available - showing raw config:"
        cat "$auth_config"
    fi

    echo ""
    echo "🔧 Available Actions:"
    echo "   1. Edit config file manually: $auth_config"
    echo "   2. Reset config: ./bot.sh setup-auth"
    echo "   3. Manage users via Telegram bot commands"
    echo ""
}

# ===============================
# Main Command Router
# ===============================

main() {
    case "${1:-help}" in
        # Deployment commands
        setup) setup_environment ;;
        upgrade) upgrade_script ;;
        bind) bind_global_command ;;

        # Telegram Auth commands
        setup-auth) setup_telegram_auth ;;
        config-auth) config_telegram_auth ;;
        unbind) unbind_global_command ;;
        start-all) start_all ;;
        stop-all) stop_all ;;
        system-status) system_status ;;
        
        # Telegram commands
        telegram) start_telegram_bot ;;
        telegram-deploy)
            echo "⚠️ 'telegram-deploy' is deprecated. Use 'telegram' for simple bot."
            echo "💡 For Docker deployment, use: docker run with manual setup"
            start_telegram_bot
            ;;
        
        # Trading commands
        start) shift; start_trading_bot "$@" ;;
        
        # Delegated commands (use Docker CLI)
        list)
            echo "📊 Running list command in Docker..."
            run_docker_cli "autotrader_cli" list
            ;;
        status)
            echo "📊 Running status command in Docker..."
            run_docker_cli "autotrader_cli" status "$2"
            ;;
        logs)
            if [[ -z "$2" ]]; then
                echo "❌ Symbol is required"
                echo "Usage: ./bot.sh logs <symbol> [lines]"
                exit 1
            fi
            # Convert symbol to container name and use simple-logs
            local symbol="$2"
            local lines="${3:-50}"
            local container_name="$(echo "$symbol" | tr '[:upper:]' '[:lower:]')usdt"  # Convert to lowercase and add usdt
            simple_logs "$container_name" "$lines"
            ;;
        simple-logs)
            simple_logs "$2" "$3"
            ;;
        stop)
            echo "🛑 Running stop command in Docker..."
            # Check if --force flag is provided
            if [[ "$3" == "--force" ]]; then
                run_docker_cli "autotrader_cli" stop "$2" --force
            else
                run_docker_cli "autotrader_cli" stop "$2"
            fi
            ;;
        restart)
            echo "🔄 Running restart command in Docker..."
            run_docker_cli "autotrader_cli" restart "$2"
            ;;
        remove)
            echo "🗑️ Running remove command in Docker..."
            # Check if --force flag is provided
            if [[ "$3" == "--force" ]]; then
                run_docker_cli "autotrader_cli" remove "$2" --force
            else
                run_docker_cli "autotrader_cli" remove "$2"
            fi
            ;;
        
        # Credential commands (Docker CLI)
        list-credentials)
            echo "🔑 Running list-credentials in Docker..."
            run_docker_cli "credentials_cli" list
            ;;
        store-credentials)
            shift # Remove "store-credentials"
            profile="$1"
            api_key="$2"
            api_secret="$3"
            display_name="$4"

            if [[ -z "$profile" || -z "$api_key" || -z "$api_secret" ]]; then
                echo "❌ Usage: ./bot.sh store-credentials <profile> <api_key> <api_secret> [display_name]"
                exit 1
            fi

            # Use non-interactive mode with all parameters
            echo "💾 Running store-credentials in Docker..."
            run_docker_cli "credentials_cli" store "$profile" --api-key "$api_key" --api-secret "$api_secret" --display-name "$display_name" --non-interactive
            ;;
        load-credentials)
            echo "📥 Running load-credentials in Docker..."
            run_docker_cli "credentials_cli" load "$2"
            ;;
        
        # Utility commands
        help|--help|-h) show_help ;;
        version|--version|-v)
            echo "🤖 AutoTrader Bot v$SCRIPT_VERSION"
            echo "📅 Compact & Enhanced Version"
            echo "🐳 Docker-based Trading System"
            ;;
        check)
            echo "🔍 System Check"
            echo "==============="
            check_docker && echo "✅ Docker: Available" || echo "❌ Docker: Not available"
            check_python && echo "✅ Python: Available" || echo "❌ Python: Not available"
            [[ -n "$TELEGRAM_BOT_TOKEN" ]] && echo "✅ Telegram: Configured" || echo "⚠️ Telegram: Not configured"
            echo "📁 Configs: $(ls $CONFIG_DIR/*.json 2>/dev/null | wc -l) files"
            echo "🐳 Containers: $(docker ps -q | wc -l) running"
            ;;
        
        *)
            echo "❌ Unknown command: $1"
            echo "💡 Use '$0 help' for available commands"
            exit 1
            ;;
    esac
}

# Run main function
main "$@"
