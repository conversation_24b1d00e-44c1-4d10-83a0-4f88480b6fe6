#!/usr/bin/env python3
"""
Wrapper script to run Central Telegram Manager with proper Python path
"""

import sys
import os
import asyncio

# Add the project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# Add src directory to Python path
src_path = os.path.join(project_root, 'src')
sys.path.insert(0, src_path)

# Now import and run the Telegram App
from run_telegram_app import start_telegram_bot
import os
import sys

if __name__ == "__main__":
    # Get credentials from environment variables (required)
    token = os.getenv('TELEGRAM_BOT_TOKEN')
    chat_id = os.getenv('TELEGRAM_CHAT_ID')

    if not token:
        print("❌ Error: TELEGRAM_BOT_TOKEN environment variable is required")
        print("💡 Set it with: export TELEGRAM_BOT_TOKEN='your_bot_token'")
        print("💡 Or create a .env file (see .env.example)")
        sys.exit(1)

    if not chat_id:
        print("❌ Error: TELEGRAM_CHAT_ID environment variable is required")
        print("💡 Set it with: export TELEGRAM_CHAT_ID='your_chat_id'")
        print("💡 Or create a .env file (see .env.example)")
        sys.exit(1)

    print(f"🤖 Starting Telegram Bot...")
    print(f"   Token: {token[:10]}...")
    print(f"   Chat ID: {chat_id}")

    start_telegram_bot(token, int(chat_id))