#!/usr/bin/env python3
"""Bot management handler for Telegram bot."""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ForceReply
from telegram.constants import ParseMode

from .base_handler import BaseTelegramHandler
from ....core.credential_utils import list_profiles
from ..templates import TelegramTemplates
from ...services.bot_creation_service import BotCreationService
from ..auth import require_auth
import re
import json
from datetime import datetime, timedelta


class BotManagementHandler(BaseTelegramHandler):
    """Handler for bot management operations."""

    def __init__(self, bot_token: str):
        """Initialize bot management handler"""
        super().__init__(bot_token)
        self.bot_creation_service = BotCreationService()

    async def handle_command(self, update, context) -> None:
        """Handle command - required by abstract base class"""
        pass

    def _parse_bot_list_output(self, output: str) -> List[Dict[str, Any]]:
        """Parse bot.sh list output into structured data"""
        containers = []
        lines = output.strip().split('\n')

        # Find the data section (after the header)
        data_started = False
        for line in lines:
            line = line.strip()

            # Skip header and separator lines
            if '==' in line or 'NAME' in line and 'STATUS' in line:
                data_started = True
                continue
            if '--' in line:
                continue
            if line.startswith('📈 Summary:') or line.startswith('📊 Summary:'):
                break

            if data_started and line and not line.startswith('⚠️'):
                # Parse container line: NAME STATUS CREATED
                parts = line.split()
                if len(parts) >= 3:
                    name = parts[0]
                    status_part = parts[1]

                    # Extract status (remove emoji)
                    status = 'running' if '🟢' in status_part else 'stopped'

                    # Extract created date (remaining parts)
                    created = ' '.join(parts[2:])

                    # Determine symbol from container name
                    symbol = self._extract_symbol_from_name(name)

                    container = {
                        'name': name,
                        'symbol': symbol,
                        'status': status,
                        'created': created,
                        'uptime': self._calculate_uptime(created) if status == 'running' else None
                    }

                    containers.append(container)

        return containers

    def _extract_symbol_from_name(self, container_name: str) -> str:
        """Extract trading symbol from container name"""
        # Container names follow pattern: {symbol}usdt or just {symbol}
        # Examples: hyperusdt, btcusdt, ethusdt, hackingtool

        name = container_name.lower()

        # Remove common suffixes and prefixes
        name = re.sub(r'[-_](bot|trader|trading|container)$', '', name)
        name = re.sub(r'^(bot|trader|trading)[-_]', '', name)

        # Remove 'usdt' suffix if present (new naming convention)
        if name.endswith('usdt'):
            symbol = name[:-4]  # Remove 'usdt'
        else:
            symbol = name

        # Handle special cases where container name doesn't match symbol
        special_cases = { }

        if symbol in special_cases:
            return special_cases[symbol]

        return symbol.upper()

    def _calculate_uptime(self, created_str: str) -> str:
        """Calculate uptime from created timestamp"""
        try:
            # Parse created timestamp (format: 2025-05-30 23:47:41)
            created = datetime.strptime(created_str, '%Y-%m-%d %H:%M:%S')
            now = datetime.now()
            uptime = now - created

            days = uptime.days
            hours, remainder = divmod(uptime.seconds, 3600)
            minutes, _ = divmod(remainder, 60)

            if days > 0:
                return f"{days}d {hours}h {minutes}m"
            elif hours > 0:
                return f"{hours}h {minutes}m"
            else:
                return f"{minutes}m"

        except Exception:
            return "Unknown"

    def _filter_trading_containers(self, containers: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Filter out non-trading containers (system, db, etc.)"""
        # List of container names/patterns to exclude
        exclude_patterns = [
            'postgres', 'redis', 'mysql', 'mongodb',
            'nginx', 'apache', 'traefik',
            'prometheus', 'grafana', 'elasticsearch',
            'rabbitmq', 'kafka', 'zookeeper',
            'docker', 'registry', 'portainer',
            'telegram-bot'  # Exclude the telegram bot itself
        ]

        filtered = []
        for container in containers:
            name = container['name'].lower()

            # Skip if matches exclude patterns
            if any(pattern in name for pattern in exclude_patterns):
                continue

            # Skip if it's clearly a system container
            if name.startswith(('system-', 'infra-', 'monitoring-')):
                continue

            filtered.append(container)

        return filtered
    
    @require_auth("USER")
    async def handle_start_bot(self, update: Update, context) -> None:
        """Handle /startbot command with format: /startbot {symbol} {amount} {dca-amount} {profile}(optional)"""
        if len(context.args) < 2:
            await self._send_error_message(
                update,
                "**Sử dụng:** `/startbot <symbol> <amount> [dca_amount] [profile]`\n\n"
                "**Ví dụ:**\n"
                "• `/startbot BTC 50 25 main` - BTC với amount=50, dca=25, profile=main\n"
                "• `/startbot ETH 100 30` - ETH với amount=100, dca=30, profile tự động\n"
                "• `/startbot SOL 75` - SOL với amount=75, không DCA, profile tự động"
            )
            return

        # Parse arguments
        symbol = context.args[0].upper()

        try:
            amount = float(context.args[1])
        except (ValueError, IndexError):
            await self._send_error_message(update, f"Amount không hợp lệ: {context.args[1] if len(context.args) > 1 else 'missing'}")
            return

        # Parse DCA amount (optional)
        dca_amount = 0.0
        if len(context.args) >= 3:
            try:
                dca_amount = float(context.args[2])
            except ValueError:
                await self._send_error_message(update, f"DCA amount không hợp lệ: {context.args[2]}")
                return

        # Parse profile (optional)
        profile = None
        if len(context.args) >= 4:
            profile = context.args[3]

        # Check for test mode flag
        test_mode = False
        if len(context.args) >= 5 and context.args[4].lower() in ["test", "testmode", "test_mode"]:
            test_mode = True

        try:
            # Show processing message
            processing_msg = await update.message.reply_text(
                f"🔄 **Đang tạo bot...**\n\n"
                f"• Symbol: `{symbol}`\n"
                f"• Amount: `${amount}`\n"
                f"• DCA Amount: `${dca_amount}`\n"
                f"• Profile: `{profile or 'auto'}`\n"
                f"• Test Mode: `{test_mode}`",
                parse_mode=ParseMode.MARKDOWN
            )

            # Create trading bot using shared service
            result = await self.bot_creation_service.create_trading_bot(
                symbol=symbol,
                amount=amount,
                dca_amount=dca_amount,
                profile=profile,
                direction="LONG",  # Default direction
                test_mode=test_mode
            )

            # Delete processing message
            try:
                await processing_msg.delete()
            except:
                pass

            if result[0] == 0:
                # Success
                await self._send_safe_message(
                    update.message.reply_text,
                    result[1],  # Success message from service
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                # Error
                await self._send_error_message(update, result[2])

        except Exception as e:
            self.logger.error(f"Error in handle_start_bot: {e}")
            await self._send_error_message(update, f"Lỗi tạo bot: {str(e)}")
    
    @require_auth("USER")
    async def handle_stop_bot(self, update: Update, context) -> None:
        """Handle /stop command for stopping trading bots"""
        if not context.args:
            await self._send_error_message(update, "Sử dụng: /stop <symbol>")
            return

        symbol = context.args[0].upper()
        
        try:
            # Create confirmation keyboard
            keyboard = [
                [
                    InlineKeyboardButton("✅ Xác nhận", callback_data=f"stop_confirm_{symbol}"),
                    InlineKeyboardButton("❌ Hủy", callback_data="stop_cancel")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                f"⚠️ Xác nhận dừng bot\n\n"
                f"Bạn có chắc muốn dừng bot {symbol}?",
                reply_markup=reply_markup,
                parse_mode=None  # Remove markdown to avoid parsing errors
            )
            
        except Exception as e:
            self.logger.error(f"Error in handle_stop_bot: {e}")
            await self._send_error_message(update, str(e))
    
    async def handle_stop_confirm(self, query, symbol: str) -> None:
        """Handle bot stop confirmation"""
        try:
            # Step 1: Stop the container using direct Docker API
            stop_success, stop_message = await self.docker_operation("stop", symbol, force=True)

            if stop_success:
                # Step 2: Remove the container after stopping
                remove_success, remove_message = await self.docker_operation("remove", symbol, force=True)

                if remove_success:
                    await query.edit_message_text(
                        f"✅ Bot {symbol} đã được dừng và xóa thành công",
                        parse_mode=None
                    )
                else:
                    # Stop succeeded but remove failed
                    await query.edit_message_text(
                        f"⚠️ Bot {symbol} đã được dừng nhưng không thể xóa container\n"
                        f"Lỗi: {remove_message}",
                        parse_mode=None
                    )
            else:
                # Stop failed
                await query.edit_message_text(
                    f"❌ Lỗi dừng bot: {stop_message}",
                    parse_mode=None
                )

        except Exception as e:
            self.logger.error(f"Error stopping bot: {e}")
            await query.edit_message_text(
                f"❌ Lỗi: {str(e)}",
                parse_mode=None
            )
    
    async def _get_enhanced_bot_list(self) -> Tuple[int, str, Optional[Any], Optional[Any]]:
        """
        Get enhanced bot list data using direct integration
        Returns: (return_code, error_message, template, keyboard)
        """
        try:
            # Use direct container retrieval instead of bot.sh
            containers = await self.get_trading_containers()

            if not containers:
                # Return empty list format for consistency
                result_output = "📭 No trading bot containers found\n\n💡 Use '/createbot' to create a new trading bot"
            else:
                # Format containers for display
                result_lines = []
                for container in containers:
                    status_emoji = "🟢" if container.get('status', '').lower() == 'running' else "🔴"
                    result_lines.append(f"{status_emoji} {container.get('name', 'unknown')} - {container.get('status', 'unknown')}")
                result_output = "\n".join(result_lines)

            # Simulate successful result format
            result = (0, result_output, "")

            # Parse the output into structured data
            all_containers = self._parse_bot_list_output(result[1])

            # Filter to only trading containers
            trading_containers = self._filter_trading_containers(all_containers)

            # Enhance container data with additional info
            enhanced_containers = []
            for container in trading_containers:
                enhanced = await self._enhance_container_info(container)
                enhanced_containers.append(enhanced)

            # Get overall stats (if available)
            stats = await self._get_trading_stats(enhanced_containers)

            # Use enhanced template
            template = TelegramTemplates.bot_list_enhanced(enhanced_containers, stats)

            # Create inline keyboard
            keyboard = self._create_inline_keyboard(template.keyboard) if template.keyboard else None

            return (0, "", template, keyboard)

        except Exception as e:
            self.logger.error(f"Error getting enhanced bot list: {e}")
            return (1, str(e), None, None)

    @require_auth("USER")
    async def handle_list_bots(self, update: Update, context) -> None:
        """Handle /list command for listing active bots with enhanced formatting"""
        try:
            # Use shared logic
            return_code, error_msg, template, keyboard = await self._get_enhanced_bot_list()

            if return_code != 0:
                await self._send_error_message(update, error_msg)
                return

            # Send message with inline keyboard
            await update.message.reply_text(
                template.content,
                parse_mode=ParseMode.HTML,
                reply_markup=keyboard
            )

        except Exception as e:
            self.logger.error(f"Error in handle_list_bots: {e}")
            await self._send_error_message(update, str(e))

    async def _enhance_container_info(self, container: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance container info with additional details"""
        enhanced = container.copy()

        try:
            # Try to get more detailed status using direct CLI
            status_result = await self.execute_cli_command("status", [container['name']])

            if status_result[0] == 0:
                # Parse status output for additional info
                status_info = self._parse_status_output(status_result[1])
                enhanced.update(status_info)

        except Exception as e:
            self.logger.debug(f"Could not enhance info for {container['name']}: {e}")

        return enhanced

    def _parse_status_output(self, output: str) -> Dict[str, Any]:
        """Parse bot status output for additional information"""
        info = {}

        try:
            # Look for common patterns in status output
            lines = output.split('\n')

            for line in lines:
                line = line.strip()

                # Extract configuration info
                if 'amount:' in line.lower():
                    amount_match = re.search(r'amount[:\s]+\$?(\d+(?:\.\d+)?)', line, re.IGNORECASE)
                    if amount_match:
                        info.setdefault('config', {})['amount'] = amount_match.group(1)

                if 'direction:' in line.lower():
                    direction_match = re.search(r'direction[:\s]+(\w+)', line, re.IGNORECASE)
                    if direction_match:
                        info.setdefault('config', {})['direction'] = direction_match.group(1)

                if 'test' in line.lower() and 'mode' in line.lower():
                    info.setdefault('config', {})['test_mode'] = True

                # Extract performance info
                if 'pnl' in line.lower() or 'profit' in line.lower():
                    # Match patterns like: $0.00, +1.23, -2.45, $+1.23, etc.
                    pnl_match = re.search(r'[\$]?([\+\-]?\d+(?:\.\d+)?)', line)
                    if pnl_match:
                        pnl_value = pnl_match.group(1)
                        # Store the numeric value without $ sign
                        info.setdefault('performance', {})['pnl'] = pnl_value

                if 'trades' in line.lower():
                    trades_match = re.search(r'(\d+)', line)
                    if trades_match:
                        info.setdefault('performance', {})['trades'] = trades_match.group(1)

        except Exception as e:
            self.logger.debug(f"Error parsing status output: {e}")

        return info

    async def _get_trading_stats(self, containers: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Calculate overall trading statistics"""
        stats = {
            'total_pnl': 0.0,
            'total_trades': 0,
            'win_rate': 0.0
        }

        try:
            total_pnl = 0.0
            total_trades = 0

            for container in containers:
                perf = container.get('performance', {})

                if perf.get('pnl') is not None:
                    try:
                        # Remove $ sign, + sign, and any whitespace
                        pnl_str = str(perf['pnl']).replace('$', '').replace('+', '').strip()
                        # Handle empty string cases
                        if pnl_str:
                            pnl = float(pnl_str)
                            total_pnl += pnl
                    except (ValueError, TypeError) as e:
                        self.logger.debug(f"Error parsing PnL '{perf['pnl']}': {e}")
                        pass

                if perf.get('trades'):
                    try:
                        trades = int(perf['trades'])
                        total_trades += trades
                    except ValueError:
                        pass

            stats['total_pnl'] = f"${total_pnl:+.2f}" if total_pnl != 0 else "$0.00"
            stats['total_trades'] = total_trades

            # Calculate win rate based on actual data
            if total_trades > 0:
                # Count winning trades (positive PnL)
                winning_trades = 0
                for container in containers:
                    perf = container.get('performance', {})
                    if perf.get('pnl') is not None:
                        try:
                            pnl_str = str(perf['pnl']).replace('$', '').replace('+', '').strip()
                            if pnl_str and float(pnl_str) > 0:
                                winning_trades += 1
                        except (ValueError, TypeError):
                            pass

                stats['win_rate'] = (winning_trades / len(containers)) * 100 if containers else 0.0
            else:
                stats['win_rate'] = 0.0

        except Exception as e:
            self.logger.debug(f"Error calculating stats: {e}")

        return stats



    @require_auth("USER")
    async def handle_stop_all_bots(self, update: Update, context) -> None:
        """Handle /stopall command for stopping all running bots"""
        try:
            # Get list of running bots using direct container retrieval
            containers = await self.get_trading_containers()

            if not containers:
                await self._send_error_message(update, "Không thể lấy danh sách containers")
                return

            # Filter for running trading containers
            running_containers = [c for c in containers if c.get('status', '').lower() == 'running']

            if not running_containers:
                await update.message.reply_text(
                    TelegramTemplates.info_message(
                        "No Running Bots",
                        "There are no running trading bots to stop."
                    ),
                    parse_mode=ParseMode.HTML
                )
                return

            # Create confirmation message
            content = f"⚠️ {TelegramTemplates.bold('Confirm Stop All Bots')}\n\n"
            content += f"You are about to stop {len(running_containers)} running bot(s):\n\n"

            for container in running_containers:
                symbol = container.get('symbol', 'Unknown')
                content += f"• 🔴 {TelegramTemplates.bold(symbol)} ({container['name']})\n"

            content += f"\n⚠️ {TelegramTemplates.bold('Warning:')} This will stop all active trading!\n"
            content += "Open positions will remain but bots will stop monitoring them.\n\n"
            content += "Are you sure you want to continue?"

            # Create confirmation keyboard
            keyboard = [
                [
                    InlineKeyboardButton("✅ Yes, Stop All", callback_data="stopall_confirm"),
                    InlineKeyboardButton("❌ Cancel", callback_data="stopall_cancel")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                content,
                reply_markup=reply_markup,
                parse_mode=ParseMode.HTML
            )

        except Exception as e:
            self.logger.error(f"Error in handle_stop_all_bots: {e}")
            await self._send_error_message(update, str(e))

    async def _execute_stop_all(self, query) -> None:
        """Execute stop all bots operation"""
        try:
            # Get running containers again (in case status changed)
            containers = await self.get_trading_containers()

            if not containers:
                await query.edit_message_text(
                    f"❌ **Error getting bot list**",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            # Filter for running containers
            running_containers = [c for c in containers if c.get('status', '').lower() == 'running']

            if not running_containers:
                await query.edit_message_text(
                    "ℹ️ **No running bots found**\n\nAll bots are already stopped.",
                    parse_mode=ParseMode.MARKDOWN
                )
                return

            # Stop each bot
            stopped_count = 0
            failed_stops = []

            status_message = f"🔄 **Stopping {len(running_containers)} bots...**\n\n"
            await query.edit_message_text(status_message, parse_mode=ParseMode.MARKDOWN)

            for container in running_containers:
                try:
                    # Use symbol instead of container name, and add --force flag
                    symbol = container.get('symbol', container['name']).replace('USDT', '').replace('usdt', '')

                    # Step 1: Stop the container using direct Docker API
                    stop_success, stop_message = await self.docker_operation("stop", symbol, force=True)

                    if stop_success:
                        # Step 2: Remove the container after stopping
                        remove_success, remove_message = await self.docker_operation("remove", symbol, force=True)

                        if remove_success:
                            stopped_count += 1
                            status_message += f"✅ {container.get('symbol', 'Unknown')} stopped & removed\n"
                        else:
                            stopped_count += 1
                            status_message += f"⚠️ {container.get('symbol', 'Unknown')} stopped (remove failed)\n"
                    else:
                        failed_stops.append(container.get('symbol', 'Unknown'))
                        status_message += f"❌ {container.get('symbol', 'Unknown')} failed to stop\n"

                    # Update status message
                    await query.edit_message_text(status_message, parse_mode=ParseMode.MARKDOWN)

                except Exception as e:
                    failed_stops.append(container.get('symbol', 'Unknown'))
                    self.logger.error(f"Error stopping {container['name']}: {e}")

            # Final summary
            final_message = f"🏁 **Stop All Complete**\n\n"
            final_message += f"✅ Successfully stopped & removed: {stopped_count}\n"

            if failed_stops:
                final_message += f"❌ Failed to stop: {len(failed_stops)}\n"
                final_message += f"Failed bots: {', '.join(failed_stops)}\n\n"
                final_message += "💡 Try stopping failed bots individually with `/stop <symbol>`"
            else:
                final_message += "\n🎉 All trading bots have been stopped and removed successfully!"

            await query.edit_message_text(final_message, parse_mode=None)

        except Exception as e:
            self.logger.error(f"Error in _execute_stop_all: {e}")
            await query.edit_message_text(
                f"❌ Lỗi trong quá trình dừng tất cả bot: {str(e)}",
                parse_mode=None
            )
    
    @require_auth("USER")
    async def handle_status_bot(self, update: Update, context) -> None:
        """Handle /status command using direct integration"""
        if not context.args:
            # Show all bots status
            await self.handle_list_bots(update, context)
            return

        symbol = context.args[0].upper()

        try:
            # Use direct CLI integration
            result = await self.execute_cli_command("status", [symbol])

            if result[0] == 0:
                await update.message.reply_text(
                    f"📊 **Trạng thái bot {symbol}:**\n\n```\n{result[1]}\n```",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await self._send_error_message(update, result[2] or result[1])

        except Exception as e:
            self.logger.error(f"Error getting bot status: {e}")
            await self._send_error_message(update, str(e))
    
    @require_auth("USER")
    async def handle_logs_bot(self, update: Update, context) -> None:
        """Handle /logs command for bot logs"""
        if not context.args:
            await self._send_error_message(update, "Sử dụng: /logs <symbol> [lines]")
            return

        symbol = context.args[0].upper()
        lines = context.args[1] if len(context.args) > 1 else "50"

        try:
            # Use direct Docker API instead of bot.sh
            lines_int = int(lines) if lines.isdigit() else 50
            success, logs_content = await self.docker_operation("logs", symbol, lines=lines_int)

            if success and logs_content:
                # Truncate if too long for Telegram (leave room for header and code formatting)
                output = logs_content
                max_length = 3800  # Leave room for header and code block formatting
                if len(output) > max_length:
                    output = output[-max_length:] + "\n\n... (truncated)"

                # Send as code block for better formatting
                await update.message.reply_text(
                    f"📋 **Logs bot {symbol}** ({lines} dòng cuối):\n\n```\n{output}\n```",
                    parse_mode=ParseMode.MARKDOWN
                )
            elif success:
                await update.message.reply_text(f"📋 **Logs {symbol}:** Không có logs")
            else:
                await self._send_error_message(update, logs_content or "Failed to get logs")

        except Exception as e:
            self.logger.error(f"Error getting bot logs: {e}")
            await self._send_error_message(update, str(e))
    
    @require_auth("USER")
    async def handle_restart_bot(self, update: Update, context) -> None:
        """Handle /restart command for restarting bots"""
        if not context.args:
            await self._send_error_message(update, "Sử dụng: /restart <symbol>")
            return

        symbol = context.args[0].upper()
        
        try:
            # Use direct CLI integration for restart
            result = await self.execute_cli_command("restart", [symbol])

            if result[0] == 0:
                await self._send_success_message(update, f"Bot {symbol} đã được khởi động lại")
            else:
                await self._send_error_message(update, result[2] or result[1])
                
        except Exception as e:
            self.logger.error(f"Error restarting bot: {e}")
            await self._send_error_message(update, str(e))

    async def handle_stop_callback(self, query, data: str) -> None:
        """Handle stop bot callback"""
        try:
            if data.startswith("stop_confirm_"):
                symbol = data.replace("stop_confirm_", "")

                # Validate symbol
                if not symbol or len(symbol.strip()) == 0:
                    await query.edit_message_text("❌ Symbol không hợp lệ")
                    return

                await self.handle_stop_confirm(query, symbol.strip())
            elif data == "stop_cancel":
                await query.edit_message_text(
                    "❌ Đã hủy dừng bot",
                    parse_mode=None
                )
            else:
                await query.edit_message_text("❌ Callback không hợp lệ")
        except Exception as e:
            self.logger.error(f"Error in handle_stop_callback: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")

    async def handle_stopall_callback(self, query, data: str) -> None:
        """Handle stop all bots callback"""
        try:
            if data == "stopall_confirm":
                await self._execute_stop_all(query)
            elif data == "stopall_cancel":
                await query.edit_message_text(
                    "❌ Đã hủy dừng tất cả bot",
                    parse_mode=None
                )
            else:
                await query.edit_message_text("❌ Callback không hợp lệ")
        except Exception as e:
            self.logger.error(f"Error in handle_stopall_callback: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")

    async def handle_list_callback(self, query) -> None:
        """Handle bot list refresh callback - uses same logic as /list command"""
        try:
            # Use shared logic to avoid code duplication
            return_code, error_msg, template, keyboard = await self._get_enhanced_bot_list()

            if return_code != 0:
                await query.edit_message_text(
                    f"❌ Không thể lấy danh sách bot: {error_msg}",
                    parse_mode=ParseMode.HTML
                )
                return

            # Edit message with proper HTML parsing
            await query.edit_message_text(
                template.content,
                parse_mode=ParseMode.HTML,  # Fixed: Use HTML parsing instead of None
                reply_markup=keyboard
            )

        except Exception as e:
            self.logger.error(f"Error in handle_list_callback: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}", parse_mode=ParseMode.HTML)

    async def handle_startall_callback(self, query) -> None:
        """Handle start all bots callback"""
        try:
            # Get list of containers using direct integration
            containers = await self.get_trading_containers()

            if not containers:
                await query.edit_message_text(
                    f"❌ Không thể lấy danh sách bot",
                    parse_mode=None
                )
                return

            stopped_containers = [c for c in containers if c.get('status', '').lower() != 'running']

            if not stopped_containers:
                await query.edit_message_text(
                    "ℹ️ Tất cả bot đã đang chạy",
                    parse_mode=None
                )
                return

            # Start all stopped containers
            success_count = 0
            for container in stopped_containers:
                symbol = container.get('symbol', 'unknown')
                try:
                    # Use direct CLI integration for start
                    start_result = await self.execute_cli_command("start", [symbol])
                    if start_result[0] == 0:
                        success_count += 1
                except Exception as e:
                    self.logger.error(f"Error starting {symbol}: {e}")

            await query.edit_message_text(
                f"✅ Đã khởi động {success_count}/{len(stopped_containers)} bot",
                parse_mode=None
            )

        except Exception as e:
            self.logger.error(f"Error in handle_startall_callback: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")

    async def handle_clean_stopped_callback(self, query) -> None:
        """Handle clean stopped bots callback"""
        try:
            # Get list of containers using direct integration
            containers = await self.get_trading_containers()

            if not containers:
                await query.edit_message_text(
                    f"❌ Không thể lấy danh sách bot",
                    parse_mode=None
                )
                return

            stopped_containers = [c for c in containers if c.get('status', '').lower() != 'running']

            if not stopped_containers:
                await query.edit_message_text(
                    "ℹ️ Không có bot nào đang dừng để dọn dẹp",
                    parse_mode=None
                )
                return

            # Remove all stopped containers
            success_count = 0
            failed_containers = []

            for container in stopped_containers:
                container_name = container.get('name', 'unknown')
                try:
                    # Use direct Docker API for remove
                    remove_success, remove_message = await self.docker_operation("remove", container_name, force=True)
                    if remove_success:
                        success_count += 1
                    else:
                        failed_containers.append(container_name)
                except Exception as e:
                    self.logger.error(f"Error removing {container_name}: {e}")
                    failed_containers.append(container_name)

            # Prepare response message
            if success_count > 0:
                message = f"🧹 Đã dọn dẹp {success_count} bot đã dừng"
                if failed_containers:
                    message += f"\n⚠️ Không thể xóa: {', '.join(failed_containers)}"
            else:
                message = f"❌ Không thể dọn dẹp bot nào"
                if failed_containers:
                    message += f"\nLỗi: {', '.join(failed_containers)}"

            await query.edit_message_text(
                message,
                parse_mode=None
            )

        except Exception as e:
            self.logger.error(f"Error in handle_clean_stopped_callback: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")

    def _create_inline_keyboard(self, keyboard_data):
        """Create inline keyboard from template data"""
        if not keyboard_data:
            return None

        keyboard = []
        for row in keyboard_data:
            button_row = []
            for button in row:
                button_row.append(InlineKeyboardButton(
                    text=button['text'],
                    callback_data=button['callback_data']
                ))
            keyboard.append(button_row)

        return InlineKeyboardMarkup(keyboard)

    async def _send_unauthorized_message(self, update: Update) -> None:
        """Send unauthorized access message to user"""
        try:
            rejection_msg = self.auth_service.get_rejection_message()

            # Add user info for admin reference
            user = update.effective_user
            user_info = f"\n\n🔍 **User Info:**\n" \
                       f"• ID: `{user.id}`\n" \
                       f"• Username: @{user.username or 'N/A'}\n" \
                       f"• Name: {user.first_name or ''} {user.last_name or ''}".strip()

            full_message = rejection_msg + user_info

            if update.message:
                await update.message.reply_text(full_message, parse_mode=ParseMode.MARKDOWN)
            elif update.callback_query:
                await update.callback_query.answer(rejection_msg, show_alert=True)

        except Exception as e:
            self.logger.error(f"Error sending unauthorized message: {e}")

