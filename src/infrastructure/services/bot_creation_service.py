#!/usr/bin/env python3
"""
Shared service for creating trading bots
Centralizes logic to avoid code duplication between different handlers
"""

import asyncio
import json
import logging
import os
from pathlib import Path
from typing import Dict, List, Optional, Tuple

from ...core.constants import CONFIG_DIR, get_config_filename
from ...core.credential_utils import list_profiles
from ..telegram.telegram_base import ValidationUtils


class BotCreationService:
    """Shared service for creating trading bots with consistent logic"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    async def create_trading_bot(
        self,
        symbol: str,
        amount: float,
        dca_amount: float = 0.0,
        profile: str = None,
        direction: str = "LONG",
        test_mode: bool = False
    ) -> Tuple[int, str, str]:
        """
        Create a trading bot with all necessary components
        
        Args:
            symbol: Trading symbol (e.g., 'BTC', 'ETH/USDT:USDT')
            amount: Trading amount in USD
            dca_amount: DCA amount in USD (optional)
            profile: Credentials profile name (optional, will use first available if None)
            direction: Trading direction ('LONG' or 'SHORT')
            test_mode: Whether to run in test mode
            
        Returns:
            Tuple[int, str, str]: (return_code, success_message, error_message)
        """
        try:
            # Step 1: Validate inputs
            validation_result = self._validate_inputs(symbol, amount, dca_amount, direction)
            if validation_result[0] != 0:
                return validation_result
                
            # Step 2: Resolve profile
            resolved_profile = await self._resolve_profile(profile)
            if not resolved_profile:
                return (1, "", "No valid credentials profile found")
                
            # Step 3: Get credentials
            credentials = await self._get_profile_credentials(resolved_profile)
            if not credentials:
                return (1, "", f"No credentials found for profile: {resolved_profile}")
                
            # Step 4: Normalize symbol and generate names
            full_symbol = self._normalize_symbol(symbol)
            container_name = self._get_container_name(symbol)
            
            # Step 5: Check if container already exists
            if await self._container_exists(container_name):
                return (1, "", f"Container '{container_name}' already exists. Use /stop {symbol} first.")
                
            # Step 6: Create config file
            config_result = await self._create_bot_config(
                full_symbol, amount, dca_amount, direction, test_mode
            )
            if config_result[0] != 0:
                return config_result
                
            # Step 7: Create Docker container
            container_result = await self._create_docker_container(
                container_name, full_symbol, amount, dca_amount, 
                direction, test_mode, credentials
            )
            
            if container_result[0] == 0:
                success_msg = (
                    f"🚀 **Bot created successfully!**\n\n"
                    f"📊 **Details:**\n"
                    f"• Symbol: `{full_symbol}`\n"
                    f"• Amount: `${amount}`\n"
                    f"• DCA Amount: `${dca_amount}`\n"
                    f"• Profile: `{resolved_profile}`\n"
                    f"• Direction: `{direction}`\n"
                    f"• Test Mode: `{test_mode}`\n"
                    f"• Container: `{container_name}`\n\n"
                    f"💡 Use `/status {symbol}` to monitor"
                )
                return (0, success_msg, "")
            else:
                return container_result
                
        except Exception as e:
            self.logger.error(f"Error creating trading bot: {e}")
            return (1, "", f"Failed to create trading bot: {str(e)}")
    
    def _validate_inputs(self, symbol: str, amount: float, dca_amount: float, direction: str) -> Tuple[int, str, str]:
        """Validate all input parameters"""
        try:
            # Validate symbol
            if not symbol or not ValidationUtils.validate_symbol(symbol):
                return (1, "", f"Invalid symbol: {symbol}")
                
            # Validate amount
            if amount <= 0:
                return (1, "", f"Amount must be positive: {amount}")
                
            # Validate DCA amount
            if dca_amount < 0:
                return (1, "", f"DCA amount cannot be negative: {dca_amount}")
                
            # Validate direction
            if direction.upper() not in ['LONG', 'SHORT']:
                return (1, "", f"Direction must be LONG or SHORT: {direction}")
                
            return (0, "", "")
            
        except Exception as e:
            return (1, "", f"Validation error: {str(e)}")
    
    async def _resolve_profile(self, profile: str = None) -> Optional[str]:
        """Resolve profile name - use provided or first available"""
        try:
            available_profiles = list_profiles()

            if not available_profiles:
                self.logger.error("No credentials profiles found")
                return None

            # Extract profile names from the list of dictionaries
            profile_names = [p['profile'] for p in available_profiles]

            if profile:
                # Validate provided profile exists
                if profile in profile_names:
                    return profile
                else:
                    self.logger.error(f"Profile '{profile}' not found. Available: {profile_names}")
                    return None
            else:
                # Use first available profile
                first_profile = profile_names[0]
                self.logger.info(f"Using first available profile: {first_profile}")
                return first_profile

        except Exception as e:
            self.logger.error(f"Error resolving profile: {e}")
            return None
    
    async def _get_profile_credentials(self, profile: str) -> Optional[Dict[str, str]]:
        """Get credentials for a profile"""
        try:
            # Read credentials directly from JSON files
            credentials_dir = "/root/.autotrader/credentials"
            profile_file = f"{credentials_dir}/{profile}.json"

            if not os.path.exists(profile_file):
                self.logger.error(f"Credentials file not found: {profile_file}")
                return None

            with open(profile_file, 'r') as f:
                credentials_data = json.load(f)

            # Extract API key and secret from JSON
            if 'api_key' in credentials_data and 'api_secret' in credentials_data:
                return {
                    'api_key': credentials_data['api_key'],
                    'api_secret': credentials_data['api_secret']
                }
            else:
                self.logger.error(f"Incomplete credentials in {profile_file}")
                return None

        except Exception as e:
            self.logger.error(f"Error getting credentials for profile {profile}: {e}")
            return None
    
    def _normalize_symbol(self, symbol: str) -> str:
        """Normalize symbol to full format"""
        if "/" not in symbol:
            # Simple symbol like "btc" -> "BTC/USDT:USDT"
            return f"{symbol.upper()}/USDT:USDT"
        return symbol
    
    def _get_container_name(self, symbol: str) -> str:
        """Generate container name from symbol"""
        # Extract base symbol (e.g., ETH/USDT:USDT -> eth, BTC/USDT:USDT -> btc)
        if "/" in symbol:
            base_symbol = symbol.split("/")[0]
        elif symbol.upper().endswith("USDT") and len(symbol) > 4:
            base_symbol = symbol[:-4]
        else:
            base_symbol = symbol

        # Convert to lowercase
        base_symbol = base_symbol.lower()

        # Remove any existing 'usdt' suffix to avoid duplication
        if base_symbol.lower().endswith('usdt'):
            base_symbol = base_symbol[:-4]

        # Add 'usdt' suffix for clarity
        return f"{base_symbol}usdt"
    
    async def _container_exists(self, container_name: str) -> bool:
        """Check if container already exists"""
        try:
            cmd = ["docker", "ps", "-a", "--format", "{{.Names}}"]
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, _ = await process.communicate()
            
            if process.returncode == 0:
                containers = stdout.decode('utf-8').strip().split('\n')
                return container_name in containers
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking container existence: {e}")
            return False
    
    async def _create_bot_config(
        self, 
        symbol: str, 
        amount: float, 
        dca_amount: float, 
        direction: str, 
        test_mode: bool
    ) -> Tuple[int, str, str]:
        """Create bot configuration file"""
        try:
            # Load template config
            template_path = CONFIG_DIR / "template.json"
            if not template_path.exists():
                return (1, "", f"Template file not found: {template_path}")
                
            with open(template_path, 'r') as f:
                config = json.load(f)

            # Update config with provided values
            config["symbol"] = symbol
            config["direction"] = direction
            config["amount"] = float(amount)
            config["use_test_mode"] = test_mode
            config["use_sandbox"] = test_mode

            # Update DCA amount in the correct nested structure
            dca_amount_float = float(dca_amount) if dca_amount else 0.0
            if dca_amount_float > 0 and "dca" in config and "strategies" in config["dca"]:
                if "BB_LOWER" in config["dca"]["strategies"]:
                    config["dca"]["strategies"]["BB_LOWER"]["amount"] = dca_amount_float
                    config["dca"]["strategies"]["BB_LOWER"]["enabled"] = True

            # Write config file
            config_filename = get_config_filename(symbol)
            config_path = CONFIG_DIR / config_filename
            with open(config_path, 'w') as f:
                json.dump(config, f, indent=2)

            self.logger.info(f"Created config file: {config_path}")
            return (0, f"Config created: {config_filename}", "")

        except Exception as e:
            self.logger.error(f"Error creating bot config: {e}")
            return (1, "", f"Failed to create config: {str(e)}")

    async def _create_docker_container(
        self,
        container_name: str,
        symbol: str,
        amount: float,
        dca_amount: float,
        direction: str,
        test_mode: bool,
        credentials: Dict[str, str]
    ) -> Tuple[int, str, str]:
        """Create Docker container for trading bot"""
        try:
            # Get config filename
            config_filename = get_config_filename(symbol)

            # Docker image
            trader_image = "autotrader:latest"

            # Get host paths for Docker in Docker
            host_config_dir = os.environ.get('HOST_CONFIG_DIR', '/app/configs')
            host_data_dir = os.environ.get('HOST_DATA_DIR', '/app/data')
            host_logs_dir = os.environ.get('HOST_LOGS_DIR', '/app/logs')

            # Build docker run command
            docker_cmd = [
                "docker", "run", "-d",
                "--name", container_name,
                "--restart", "unless-stopped",
                "-e", f"BYBIT_API_KEY={credentials['api_key']}",
                "-e", f"BYBIT_API_SECRET={credentials['api_secret']}",
                "-e", f"BOT_CONFIG_FILE=configs/{config_filename}",
                "-e", f"TRADE_SYMBOL={symbol}",
                "-e", f"TRADE_AMOUNT={amount}",
                "-e", f"TRADE_DIRECTION={direction}",
                "-e", f"TEST_MODE={str(test_mode).lower()}",
                "-v", f"{host_config_dir}:/app/configs",
                "-v", f"{host_data_dir}:/app/data",
                "-v", f"{host_logs_dir}:/app/logs",
                trader_image,
                "python", "main.py", "--start"
            ]

            if test_mode:
                docker_cmd.append("--test")

            # Execute docker command
            result = await self._execute_docker_cmd(docker_cmd)

            if result[0] == 0:
                container_id = result[1].strip()
                self.logger.info(f"Created trading bot container: {container_id[:12]}")
                return (0, f"Container {container_name} created successfully", "")
            else:
                error_msg = result[2] or result[1]
                self.logger.error(f"Failed to create container: {error_msg}")
                return (1, "", error_msg)

        except Exception as e:
            self.logger.error(f"Error creating Docker container: {e}")
            return (1, "", f"Failed to create container: {str(e)}")

    async def _execute_docker_cmd(self, cmd: List[str]) -> Tuple[int, str, str]:
        """Execute docker command with security validation"""
        try:
            # Security: Validate command structure
            if not cmd or len(cmd) == 0:
                raise ValueError("Empty command not allowed")

            # Security: Only allow docker commands
            if cmd[0] != "docker":
                raise ValueError(f"Only docker commands allowed, got: {cmd[0]}")

            # Security: Validate all command arguments
            for arg in cmd:
                if not isinstance(arg, str):
                    raise ValueError(f"Invalid argument type: {type(arg)}")

                # Limit argument length
                if len(arg) > 200:
                    raise ValueError(f"Argument too long: {len(arg)} chars")

            # Security: Log the command for audit
            safe_cmd = ' '.join(cmd)
            self.logger.info(f"Executing docker command: {safe_cmd}")

            # Run docker command asynchronously
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd="/app"
            )

            stdout, stderr = await process.communicate()

            return (
                process.returncode,
                stdout.decode('utf-8', errors='ignore').strip(),
                stderr.decode('utf-8', errors='ignore').strip()
            )

        except Exception as e:
            self.logger.error(f"Error executing docker command {' '.join(cmd) if cmd else 'None'}: {e}")
            return 1, "", str(e)
