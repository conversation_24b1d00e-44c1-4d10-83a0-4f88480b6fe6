#!/usr/bin/env python3
"""Container helper utilities for Telegram bot commands"""

import subprocess
import json
import re
from typing import List, Dict, Optional, Tuple
import logging

logger = logging.getLogger(__name__)

class ContainerHelper:
    """Helper class for Docker container operations"""
    
    def __init__(self, docker_cmd: str = "docker"):
        self.docker_cmd = docker_cmd
    
    def _run_command(self, cmd: List[str]) -> Tuple[int, str, str]:
        """Run a command and return (returncode, stdout, stderr)"""
        try:
            result = subprocess.run(
                cmd, capture_output=True, text=True, timeout=30
            )
            return result.returncode, result.stdout.strip(), result.stderr.strip()
        except subprocess.TimeoutExpired:
            return 1, "", "Command timed out"
        except Exception as e:
            return 1, "", str(e)
    
    def normalize_symbol(self, symbol: str) -> str:
        """Normalize symbol to container name format - consistent with shell_constants.sh"""
        if not symbol:
            return ""

        # Handle different symbol formats
        if "/" in symbol:
            # Full format like "ETH/USDT:USDT" -> "eth"
            base_symbol = symbol.split("/")[0]
        elif symbol.upper().endswith("USDT") and len(symbol) > 4:
            # Format like "ETHUSDT" -> "eth"
            base_symbol = symbol[:-4]
        else:
            # Simple format like "ETH" or "eth" -> "eth"
            base_symbol = symbol

        # Convert to lowercase
        base_symbol = base_symbol.lower()

        # Remove any existing 'usdt' suffix to avoid duplication
        if base_symbol.lower().endswith('usdt'):
            base_symbol = base_symbol[:-4]

        # Add 'usdt' suffix for clarity - consistent with shell_constants.sh
        return f"{base_symbol}usdt"
    
    def find_container_by_symbol(self, symbol: str) -> Optional[str]:
        """Find container by symbol with fuzzy matching"""
        normalized = self.normalize_symbol(symbol)
        if not normalized:
            return None
        
        # Get all container names
        returncode, stdout, stderr = self._run_command([
            self.docker_cmd, "ps", "-a", "--format", "{{.Names}}"
        ])
        
        if returncode != 0:
            logger.error(f"Failed to list containers: {stderr}")
            return None
        
        containers = stdout.split('\n') if stdout else []
        
        # First try exact match
        if normalized in containers:
            return normalized
        
        # Try fuzzy search
        for container in containers:
            if normalized in container.lower():
                return container
        
        return None
    
    def get_container_status(self, container_name: str) -> Optional[Dict]:
        """Get detailed container status"""
        if not container_name:
            return None
        
        # Get container info
        returncode, stdout, stderr = self._run_command([
            self.docker_cmd, "ps", "-a", "--filter", f"name={container_name}",
            "--format", "{{.Names}}\t{{.Status}}\t{{.Image}}\t{{.CreatedAt}}"
        ])
        
        if returncode != 0 or not stdout:
            return None
        
        lines = stdout.split('\n')
        if not lines or not lines[0]:
            return None
        
        parts = lines[0].split('\t')
        if len(parts) < 4:
            return None
        
        status_info = {
            'name': parts[0],
            'status': parts[1],
            'image': parts[2],
            'created': parts[3],
            'running': 'Up' in parts[1]
        }
        
        # Get additional stats if running
        if status_info['running']:
            stats_returncode, stats_stdout, _ = self._run_command([
                self.docker_cmd, "stats", "--no-stream", "--format",
                "{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}", container_name
            ])
            
            if stats_returncode == 0 and stats_stdout:
                stats_parts = stats_stdout.split('\t')
                if len(stats_parts) >= 3:
                    status_info['cpu'] = stats_parts[0]
                    status_info['memory'] = stats_parts[1]
                    status_info['network'] = stats_parts[2]
        
        return status_info
    
    def get_container_logs(self, container_name: str, lines: int = 50) -> Optional[str]:
        """Get container logs"""
        if not container_name:
            return None
        
        returncode, stdout, stderr = self._run_command([
            self.docker_cmd, "logs", "--tail", str(lines), "--timestamps", container_name
        ])
        
        if returncode != 0:
            return f"Error getting logs: {stderr}"
        
        return stdout
    
    def list_trading_containers(self) -> List[Dict]:
        """List all trading bot containers"""
        returncode, stdout, stderr = self._run_command([
            self.docker_cmd, "ps", "-a", "--format",
            "{{.Names}}\t{{.Status}}\t{{.Image}}\t{{.CreatedAt}}"
        ])
        
        if returncode != 0:
            logger.error(f"Failed to list containers: {stderr}")
            return []
        
        containers = []
        lines = stdout.split('\n') if stdout else []
        
        for line in lines:
            if not line:
                continue
            
            parts = line.split('\t')
            if len(parts) < 4:
                continue
            
            name = parts[0]
            
            # Filter out system containers
            if any(sys_name in name for sys_name in ['autotrader-telegram', 'postgres', 'redis', 'nginx']):
                continue
            
            # Only include containers that look like trading bots (simple names)
            if re.match(r'^[a-z]+$', name):
                containers.append({
                    'name': name,
                    'status': parts[1],
                    'image': parts[2],
                    'created': parts[3],
                    'running': 'Up' in parts[1]
                })
        
        return containers
    
    def stop_container(self, container_name: str) -> Tuple[bool, str]:
        """Stop a container"""
        if not container_name:
            return False, "Container name is required"
        
        returncode, stdout, stderr = self._run_command([
            self.docker_cmd, "stop", container_name
        ])
        
        if returncode == 0:
            return True, f"Container {container_name} stopped successfully"
        else:
            return False, f"Failed to stop container: {stderr}"
    
    def restart_container(self, container_name: str) -> Tuple[bool, str]:
        """Restart a container"""
        if not container_name:
            return False, "Container name is required"
        
        returncode, stdout, stderr = self._run_command([
            self.docker_cmd, "restart", container_name
        ])
        
        if returncode == 0:
            return True, f"Container {container_name} restarted successfully"
        else:
            return False, f"Failed to restart container: {stderr}"
    
    def remove_container(self, container_name: str) -> Tuple[bool, str]:
        """Remove a container"""
        if not container_name:
            return False, "Container name is required"
        
        # Stop first
        self._run_command([self.docker_cmd, "stop", container_name])
        
        # Then remove
        returncode, stdout, stderr = self._run_command([
            self.docker_cmd, "rm", container_name
        ])
        
        if returncode == 0:
            return True, f"Container {container_name} removed successfully"
        else:
            return False, f"Failed to remove container: {stderr}"

# Global instance
container_helper = ContainerHelper()
